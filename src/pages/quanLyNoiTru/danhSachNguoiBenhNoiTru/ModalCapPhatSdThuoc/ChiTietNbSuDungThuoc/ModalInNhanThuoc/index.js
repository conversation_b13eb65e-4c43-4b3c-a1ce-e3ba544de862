import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { TreeSelect, Form, message } from "antd";

import { Button, ModalTemplate } from "components";
import { Main } from "./styled";

const ModalInNhanThuoc = (_, ref) => {
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  const [state, _setState] = useState({
    show: false,
    dsThuoc: [],
    selectedMedications: [],
  });
  const setState = (data = {}) => _setState((state) => ({ ...state, ...data }));

  const { t } = useTranslation();

  useImperativeHandle(ref, () => ({
    show: ({ dsThuoc, label }, callback) => {
      setState({
        show: true,
        label,
        dsThuoc: dsThuoc || [],
        selectedMedications: [],
      });
      form.resetFields();
      refCallback.current = callback;
    },
  }));

  const treeData = useMemo(() => {
    return (state.dsThuoc || []).map((thuoc) => {
      const children = (thuoc.dsThoiGianSuDung || []).map((thoiGian) => {
        const tuThoiGian = thoiGian.tuThoiGian
          ? moment(thoiGian.tuThoiGian).format("DD/MM/YYYY HH:mm:ss")
          : "";
        const denThoiGian = thoiGian.denThoiGian
          ? moment(thoiGian.denThoiGian).format("DD/MM/YYYY HH:mm:ss")
          : "";

        const timeLabel =
          tuThoiGian && denThoiGian
            ? `${thuoc.tenDichVu}: ${tuThoiGian} - ${denThoiGian}`
            : thuoc.tenDichVu + " " + (tuThoiGian || denThoiGian);

        const key = `${thuoc.id}_${thoiGian.tuThoiGian || ""}_${
          thoiGian.denThoiGian || ""
        }`;

        return {
          title: timeLabel,
          value: key,
          key: key,
          thuocId: thuoc.id,
          thoiGianData: thoiGian,
        };
      });

      return {
        title: thuoc.tenDichVu,
        value: thuoc.id,
        key: thuoc.id,
        loai: thuoc.loai,
        children: children,
        selectable: false,
      };
    });
  }, [state.dsThuoc]);

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    if (!state.show || !treeData?.length) return;

    // Auto chọn child đầu tiên của từng tree data hoặc node cha nếu không có children
    const autoSelectedValues = treeData.map((node) => {
      if (node.children?.length > 0) {
        // Có children thì chọn child đầu tiên
        return node.children[0].value;
      } else {
        // Không có children thì chọn node cha
        return node.value;
      }
    });

    setState({ selectedMedications: autoSelectedValues });
    form.setFieldsValue({ selectedMedications: autoSelectedValues });
  }, [state.show, treeData]);

  const onClose = () => {
    setState({ show: false, selectedMedications: [] });
    form.resetFields();
  };

  const onTreeSelectChange = (selectedValues) => {
    setState({ selectedMedications: selectedValues });
  };

  const onSubmit = async () => {
    try {
      const values = await form.validateFields();
      const { selectedMedications } = values;

      if (!selectedMedications || selectedMedications.length === 0) {
        message.error(t("common.vuiLongChonThuoc"));
        return;
      }

      const groupedByMedication = {};

      selectedMedications.forEach((selectedValue) => {
        if (selectedValue.includes("_")) {
          // Xử lý children (có thời gian)
          const [thuocId, thoiGianIndex] = selectedValue.split("_");
          const medication = state.dsThuoc.find(
            (m) => m.id === parseInt(thuocId)
          );
          const thoiGianData =
            medication?.dsThoiGianSuDung?.[parseInt(thoiGianIndex)];

          if (!groupedByMedication[thuocId]) {
            groupedByMedication[thuocId] = {
              id: parseInt(thuocId),
              loai: medication?.loai || 10,
              dsThoiGianSuDung: [],
            };
          }

          if (thoiGianData) {
            groupedByMedication[thuocId].dsThoiGianSuDung.push({
              tuThoiGian: thoiGianData.tuThoiGian || "",
              denThoiGian: thoiGianData.denThoiGian || "",
            });
          }
        } else {
          // Xử lý node cha (không có thời gian)
          const thuocId = selectedValue;
          const medication = state.dsThuoc.find(
            (m) => m.id === parseInt(thuocId)
          );

          if (medication && !groupedByMedication[thuocId]) {
            groupedByMedication[thuocId] = {
              id: parseInt(thuocId),
              loai: medication?.loai || 10,
              dsThoiGianSuDung: [], // Không có thời gian thì để array rỗng
            };
          }
        }
      });

      const dsThuoc = Object.values(groupedByMedication);

      if (refCallback.current) {
        refCallback.current({ dsThuoc });
      }

      onClose();
    } catch (error) {
      console.error("Form validation failed:", error);
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      title={state.label}
      width={"min(95vw,600px)"}
      onCancel={onClose}
      actionRight={
        <>
          <Button onClick={onClose}>{t("common.huy")}</Button>
          <Button onClick={onSubmit} type="primary">
            {t("common.xacNhan")}
          </Button>
        </>
      }
      deStoyOnClose
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            selectedMedications: [],
          }}
        >
          <Form.Item
            label={t("quanLyNoiTru.capPhatThuoc.chonThuocVaThoiGian")}
            name="selectedMedications"
            rules={[
              {
                required: true,
                message: t(
                  "quanLyNoiTru.capPhatThuoc.vuiLongChonThuocVaThoiGian"
                ),
              },
            ]}
          >
            <TreeSelect
              treeData={treeData}
              treeCheckable={true}
              showCheckedStrategy={TreeSelect.SHOW_PARENT}
              placeholder={t(
                "quanLyNoiTru.capPhatThuoc.chonThuocVaThoiGianCanIn"
              )}
              style={{ width: "100%" }}
              onChange={onTreeSelectChange}
              treeDefaultExpandAll
            />
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalInNhanThuoc);
